namespace NotifyMasterApi.Services;

public sealed class NotificationService : INotificationService
{
    private readonly IQueueService _queueService;

    public NotificationService(IQueueService queueService)
    {
        _queueService = queueService;
    }

    public async Task SendAsync(SendNotificationRequest request)
    {
        var tasks = new List<Task>();

        // Process each channel specified in the request
        foreach (var channel in request.Channels)
        {
            switch (channel.ToLowerInvariant())
            {
                case "email":
                    if (request.Recipients.Any(r => !string.IsNullOrEmpty(r.Email)))
                    {
                        tasks.Add(ProcessEmailNotifications(request));
                    }
                    break;

                case "sms":
                    if (request.Recipients.Any(r => !string.IsNullOrEmpty(r.PhoneNumber)))
                    {
                        tasks.Add(ProcessSmsNotifications(request));
                    }
                    break;

                case "push":
                    if (request.Recipients.Any(r => !string.IsNullOrEmpty(r.<PERSON>ceToken)))
                    {
                        tasks.Add(ProcessPushNotifications(request));
                    }
                    break;

                case "webapp":
                    if (request.Recipients.Any(r => !string.IsNullOrEmpty(r.UserId)))
                    {
                        tasks.Add(ProcessWebAppNotifications(request));
                    }
                    break;
            }
        }

        await Task.WhenAll(tasks);
    }

    private async Task ProcessEmailNotifications(SendNotificationRequest request)
    {
        // Create email-specific queue messages for each recipient
        foreach (var recipient in request.Recipients.Where(r => !string.IsNullOrEmpty(r.Email)))
        {
            var emailMessage = new
            {
                Id = Guid.NewGuid().ToString(),
                TenantId = request.TenantId,
                UserId = request.UserId,
                CorrelationId = request.CorrelationId,
                Recipient = recipient.Email,
                Subject = request.Message.Subject,
                Content = request.Message.Content,
                From = request.Message.From,
                Headers = request.Message.Headers ?? new Dictionary<string, string>(),
                Priority = request.Priority,
                ScheduledFor = request.ScheduledAt,
                Metadata = request.Metadata
            };

            await _queueService.PublishAsync("email-queue", emailMessage);
        }
    }

    private async Task ProcessSmsNotifications(SendNotificationRequest request)
    {
        // Create SMS-specific queue messages for each recipient
        foreach (var recipient in request.Recipients.Where(r => !string.IsNullOrEmpty(r.PhoneNumber)))
        {
            var smsMessage = new
            {
                Id = Guid.NewGuid().ToString(),
                TenantId = request.TenantId,
                UserId = request.UserId,
                CorrelationId = request.CorrelationId,
                Recipient = recipient.PhoneNumber,
                Content = request.Message.Content,
                From = request.Message.From,
                Priority = request.Priority,
                ScheduledFor = request.ScheduledAt,
                Metadata = request.Metadata
            };

            await _queueService.PublishAsync("sms-queue", smsMessage);
        }
    }

    private async Task ProcessPushNotifications(SendNotificationRequest request)
    {
        // Create push notification queue messages for each recipient
        foreach (var recipient in request.Recipients.Where(r => !string.IsNullOrEmpty(r.DeviceToken)))
        {
            var pushMessage = new
            {
                Id = Guid.NewGuid().ToString(),
                TenantId = request.TenantId,
                UserId = request.UserId,
                CorrelationId = request.CorrelationId,
                DeviceToken = recipient.DeviceToken,
                Title = request.Message.Subject,
                Content = request.Message.Content,
                Data = request.Message.PushNotificationData,
                Priority = request.Priority,
                ScheduledFor = request.ScheduledAt,
                Metadata = request.Metadata
            };

            await _queueService.PublishAsync("push-queue", pushMessage);
        }
    }

    private async Task ProcessWebAppNotifications(SendNotificationRequest request)
    {
        // Create web app notification queue messages for each recipient
        foreach (var recipient in request.Recipients.Where(r => !string.IsNullOrEmpty(r.UserId)))
        {
            var webAppMessage = new
            {
                Id = Guid.NewGuid().ToString(),
                TenantId = request.TenantId,
                UserId = recipient.UserId,
                CorrelationId = request.CorrelationId,
                Title = request.Message.Subject,
                Content = request.Message.Content,
                Data = request.Message.WebAppNotificationData,
                Priority = request.Priority,
                ScheduledFor = request.ScheduledAt,
                Metadata = request.Metadata
            };

            await _queueService.PublishAsync("webapp-queue", webAppMessage);
        }
    }
}