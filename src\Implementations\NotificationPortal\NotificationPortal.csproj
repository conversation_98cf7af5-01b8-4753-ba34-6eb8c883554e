<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Authorization" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.6" />
    <PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
    <PackageReference Include="Blazored.Toast" Version="4.2.1" />
    <PackageReference Include="MudBlazor" Version="7.8.0" />
    <PackageReference Include="Blazor-ApexCharts" Version="6.0.1" />
    <PackageReference Include="System.Text.Json" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Components.DataAnnotations.Validation" Version="3.2.0-rc1.20223.4" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Localization" Version="2.3.0" />
    <PackageReference Include="BlazorMonaco" Version="3.3.0" />
    <PackageReference Include="Markdig" Version="0.41.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NotifyMasterApi\NotifyMasterApi.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Components\Layout\" />
    <Folder Include="Components\Shared\" />
    <Folder Include="Pages\Auth\" />
    <Folder Include="Pages\Admin\" />
    <Folder Include="Services\" />
    <Folder Include="Models\" />
    <Folder Include="Hubs\" />
    <Folder Include="wwwroot\css\" />
    <Folder Include="wwwroot\js\" />
    <Folder Include="wwwroot\images\" />
  </ItemGroup>

</Project>
