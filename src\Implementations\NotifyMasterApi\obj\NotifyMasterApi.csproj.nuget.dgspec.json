{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Implementations\\NotifyMasterApi\\NotifyMasterApi.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj", "projectName": "NotifyMaster.Core", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj"}, "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Hangfire.Core": {"target": "Package", "version": "[1.8.20, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[9.0.0-preview.1, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.12.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[9.0.5, 9.0.5]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.5.25277.114/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(, 4.7.32767]", "Microsoft.NETCore.App": "(, 2.1.32767]", "Microsoft.VisualBasic": "(, 10.4.32767]", "Microsoft.Win32.Primitives": "(, 4.3.32767]", "Microsoft.Win32.Registry": "(, 5.0.32767]", "runtime.any.System.Collections": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.any.System.Globalization": "(, 4.3.32767]", "runtime.any.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.any.System.IO": "(, 4.3.32767]", "runtime.any.System.Reflection": "(, 4.3.32767]", "runtime.any.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.any.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.any.System.Runtime": "(, 4.3.32767]", "runtime.any.System.Runtime.Handles": "(, 4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.any.System.Text.Encoding": "(, 4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.any.System.Threading.Tasks": "(, 4.3.32767]", "runtime.any.System.Threading.Timer": "(, 4.3.32767]", "runtime.aot.System.Collections": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.aot.System.Globalization": "(, 4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.aot.System.IO": "(, 4.3.32767]", "runtime.aot.System.Reflection": "(, 4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.aot.System.Runtime": "(, 4.3.32767]", "runtime.aot.System.Runtime.Handles": "(, 4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.aot.System.Threading.Tasks": "(, 4.3.32767]", "runtime.aot.System.Threading.Timer": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.unix.System.Console": "(, 4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.unix.System.IO.FileSystem": "(, 4.3.32767]", "runtime.unix.System.Net.Primitives": "(, 4.3.32767]", "runtime.unix.System.Net.Sockets": "(, 4.3.32767]", "runtime.unix.System.Private.Uri": "(, 4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.win.System.Console": "(, 4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.win.System.IO.FileSystem": "(, 4.3.32767]", "runtime.win.System.Net.Primitives": "(, 4.3.32767]", "runtime.win.System.Net.Sockets": "(, 4.3.32767]", "runtime.win.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7.System.Private.Uri": "(, 4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(, 4.3.32767]", "System.AppContext": "(, 4.3.32767]", "System.Buffers": "(, 5.0.32767]", "System.Collections": "(, 4.3.32767]", "System.Collections.Concurrent": "(, 4.3.32767]", "System.Collections.Immutable": "(, 9.0.32767]", "System.Collections.NonGeneric": "(, 4.3.32767]", "System.Collections.Specialized": "(, 4.3.32767]", "System.ComponentModel": "(, 4.3.32767]", "System.ComponentModel.Annotations": "(, 5.0.32767]", "System.ComponentModel.EventBasedAsync": "(, 4.3.32767]", "System.ComponentModel.Primitives": "(, 4.3.32767]", "System.ComponentModel.TypeConverter": "(, 4.3.32767]", "System.Console": "(, 4.3.32767]", "System.Data.Common": "(, 4.3.32767]", "System.Data.DataSetExtensions": "(, 4.5.32767]", "System.Diagnostics.Contracts": "(, 4.3.32767]", "System.Diagnostics.Debug": "(, 4.3.32767]", "System.Diagnostics.DiagnosticSource": "(, 9.0.32767]", "System.Diagnostics.FileVersionInfo": "(, 4.3.32767]", "System.Diagnostics.Process": "(, 4.3.32767]", "System.Diagnostics.StackTrace": "(, 4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(, 4.3.32767]", "System.Diagnostics.Tools": "(, 4.3.32767]", "System.Diagnostics.TraceSource": "(, 4.3.32767]", "System.Diagnostics.Tracing": "(, 4.3.32767]", "System.Drawing.Primitives": "(, 4.3.32767]", "System.Dynamic.Runtime": "(, 4.3.32767]", "System.Formats.Asn1": "(, 9.0.32767]", "System.Formats.Tar": "(, 9.0.32767]", "System.Globalization": "(, 4.3.32767]", "System.Globalization.Calendars": "(, 4.3.32767]", "System.Globalization.Extensions": "(, 4.3.32767]", "System.IO": "(, 4.3.32767]", "System.IO.Compression": "(, 4.3.32767]", "System.IO.Compression.ZipFile": "(, 4.3.32767]", "System.IO.FileSystem": "(, 4.3.32767]", "System.IO.FileSystem.AccessControl": "(, 5.0.32767]", "System.IO.FileSystem.DriveInfo": "(, 4.3.32767]", "System.IO.FileSystem.Primitives": "(, 4.3.32767]", "System.IO.FileSystem.Watcher": "(, 4.3.32767]", "System.IO.IsolatedStorage": "(, 4.3.32767]", "System.IO.MemoryMappedFiles": "(, 4.3.32767]", "System.IO.Pipelines": "(, 9.0.32767]", "System.IO.Pipes": "(, 4.3.32767]", "System.IO.Pipes.AccessControl": "(, 4.6.32767]", "System.IO.UnmanagedMemoryStream": "(, 4.3.32767]", "System.Linq": "(, 4.3.32767]", "System.Linq.Expressions": "(, 4.3.32767]", "System.Linq.Parallel": "(, 4.3.32767]", "System.Linq.Queryable": "(, 4.3.32767]", "System.Memory": "(, 5.0.32767]", "System.Net.Http": "(, 4.3.32767]", "System.Net.Http.Json": "(, 9.0.32767]", "System.Net.NameResolution": "(, 4.3.32767]", "System.Net.NetworkInformation": "(, 4.3.32767]", "System.Net.Ping": "(, 4.3.32767]", "System.Net.Primitives": "(, 4.3.32767]", "System.Net.Requests": "(, 4.3.32767]", "System.Net.Security": "(, 4.3.32767]", "System.Net.Sockets": "(, 4.3.32767]", "System.Net.WebHeaderCollection": "(, 4.3.32767]", "System.Net.WebSockets": "(, 4.3.32767]", "System.Net.WebSockets.Client": "(, 4.3.32767]", "System.Numerics.Vectors": "(, 5.0.32767]", "System.ObjectModel": "(, 4.3.32767]", "System.Private.DataContractSerialization": "(, 4.3.32767]", "System.Private.Uri": "(, 4.3.32767]", "System.Reflection": "(, 4.3.32767]", "System.Reflection.DispatchProxy": "(, 6.0.32767]", "System.Reflection.Emit": "(, 4.7.32767]", "System.Reflection.Emit.ILGeneration": "(, 4.7.32767]", "System.Reflection.Emit.Lightweight": "(, 4.7.32767]", "System.Reflection.Extensions": "(, 4.3.32767]", "System.Reflection.Metadata": "(, 9.0.32767]", "System.Reflection.Primitives": "(, 4.3.32767]", "System.Reflection.TypeExtensions": "(, 4.7.32767]", "System.Resources.Reader": "(, 4.3.32767]", "System.Resources.ResourceManager": "(, 4.3.32767]", "System.Resources.Writer": "(, 4.3.32767]", "System.Runtime": "(, 4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(, 7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(, 4.3.32767]", "System.Runtime.Extensions": "(, 4.3.32767]", "System.Runtime.Handles": "(, 4.3.32767]", "System.Runtime.InteropServices": "(, 4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(, 4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(, 4.3.32767]", "System.Runtime.Loader": "(, 4.3.32767]", "System.Runtime.Numerics": "(, 4.3.32767]", "System.Runtime.Serialization.Formatters": "(, 4.3.32767]", "System.Runtime.Serialization.Json": "(, 4.3.32767]", "System.Runtime.Serialization.Primitives": "(, 4.3.32767]", "System.Runtime.Serialization.Xml": "(, 4.3.32767]", "System.Runtime.WindowsRuntime": "(, 4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(, 4.7.32767]", "System.Security.AccessControl": "(, 6.0.32767]", "System.Security.Claims": "(, 4.3.32767]", "System.Security.Cryptography.Algorithms": "(, 4.3.32767]", "System.Security.Cryptography.Cng": "(, 4.6.32767]", "System.Security.Cryptography.Csp": "(, 4.3.32767]", "System.Security.Cryptography.Encoding": "(, 4.3.32767]", "System.Security.Cryptography.OpenSsl": "(, 5.0.32767]", "System.Security.Cryptography.Primitives": "(, 4.3.32767]", "System.Security.Cryptography.X509Certificates": "(, 4.3.32767]", "System.Security.Cryptography.Xml": "(, 4.4.32767]", "System.Security.Principal": "(, 4.3.32767]", "System.Security.Principal.Windows": "(, 5.0.32767]", "System.Security.SecureString": "(, 4.3.32767]", "System.Text.Encoding": "(, 4.3.32767]", "System.Text.Encoding.CodePages": "(, 9.0.32767]", "System.Text.Encoding.Extensions": "(, 4.3.32767]", "System.Text.Encodings.Web": "(, 9.0.32767]", "System.Text.Json": "(, 9.0.32767]", "System.Text.RegularExpressions": "(, 4.3.32767]", "System.Threading": "(, 4.3.32767]", "System.Threading.Channels": "(, 9.0.32767]", "System.Threading.Overlapped": "(, 4.3.32767]", "System.Threading.Tasks": "(, 4.3.32767]", "System.Threading.Tasks.Dataflow": "(, 9.0.32767]", "System.Threading.Tasks.Extensions": "(, 5.0.32767]", "System.Threading.Tasks.Parallel": "(, 4.3.32767]", "System.Threading.Thread": "(, 4.3.32767]", "System.Threading.ThreadPool": "(, 4.3.32767]", "System.Threading.Timer": "(, 4.3.32767]", "System.ValueTuple": "(, 4.5.32767]", "System.Xml.ReaderWriter": "(, 4.3.32767]", "System.Xml.XDocument": "(, 4.3.32767]", "System.Xml.XmlDocument": "(, 4.3.32767]", "System.Xml.XmlSerializer": "(, 4.3.32767]", "System.Xml.XPath": "(, 4.3.32767]", "System.Xml.XPath.XDocument": "(, 5.0.32767]"}}}}, "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj", "projectName": "NotifyMaster.Database", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Database\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Entities\\NotifyMaster.Entities.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Entities\\NotifyMaster.Entities.csproj"}, "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[9.0.5, 9.0.5]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.5.25277.114/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(, 4.7.32767]", "Microsoft.NETCore.App": "(, 2.1.32767]", "Microsoft.VisualBasic": "(, 10.4.32767]", "Microsoft.Win32.Primitives": "(, 4.3.32767]", "Microsoft.Win32.Registry": "(, 5.0.32767]", "runtime.any.System.Collections": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.any.System.Globalization": "(, 4.3.32767]", "runtime.any.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.any.System.IO": "(, 4.3.32767]", "runtime.any.System.Reflection": "(, 4.3.32767]", "runtime.any.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.any.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.any.System.Runtime": "(, 4.3.32767]", "runtime.any.System.Runtime.Handles": "(, 4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.any.System.Text.Encoding": "(, 4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.any.System.Threading.Tasks": "(, 4.3.32767]", "runtime.any.System.Threading.Timer": "(, 4.3.32767]", "runtime.aot.System.Collections": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.aot.System.Globalization": "(, 4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.aot.System.IO": "(, 4.3.32767]", "runtime.aot.System.Reflection": "(, 4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.aot.System.Runtime": "(, 4.3.32767]", "runtime.aot.System.Runtime.Handles": "(, 4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.aot.System.Threading.Tasks": "(, 4.3.32767]", "runtime.aot.System.Threading.Timer": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.unix.System.Console": "(, 4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.unix.System.IO.FileSystem": "(, 4.3.32767]", "runtime.unix.System.Net.Primitives": "(, 4.3.32767]", "runtime.unix.System.Net.Sockets": "(, 4.3.32767]", "runtime.unix.System.Private.Uri": "(, 4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.win.System.Console": "(, 4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.win.System.IO.FileSystem": "(, 4.3.32767]", "runtime.win.System.Net.Primitives": "(, 4.3.32767]", "runtime.win.System.Net.Sockets": "(, 4.3.32767]", "runtime.win.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7.System.Private.Uri": "(, 4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(, 4.3.32767]", "System.AppContext": "(, 4.3.32767]", "System.Buffers": "(, 5.0.32767]", "System.Collections": "(, 4.3.32767]", "System.Collections.Concurrent": "(, 4.3.32767]", "System.Collections.Immutable": "(, 9.0.32767]", "System.Collections.NonGeneric": "(, 4.3.32767]", "System.Collections.Specialized": "(, 4.3.32767]", "System.ComponentModel": "(, 4.3.32767]", "System.ComponentModel.Annotations": "(, 5.0.32767]", "System.ComponentModel.EventBasedAsync": "(, 4.3.32767]", "System.ComponentModel.Primitives": "(, 4.3.32767]", "System.ComponentModel.TypeConverter": "(, 4.3.32767]", "System.Console": "(, 4.3.32767]", "System.Data.Common": "(, 4.3.32767]", "System.Data.DataSetExtensions": "(, 4.5.32767]", "System.Diagnostics.Contracts": "(, 4.3.32767]", "System.Diagnostics.Debug": "(, 4.3.32767]", "System.Diagnostics.DiagnosticSource": "(, 9.0.32767]", "System.Diagnostics.FileVersionInfo": "(, 4.3.32767]", "System.Diagnostics.Process": "(, 4.3.32767]", "System.Diagnostics.StackTrace": "(, 4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(, 4.3.32767]", "System.Diagnostics.Tools": "(, 4.3.32767]", "System.Diagnostics.TraceSource": "(, 4.3.32767]", "System.Diagnostics.Tracing": "(, 4.3.32767]", "System.Drawing.Primitives": "(, 4.3.32767]", "System.Dynamic.Runtime": "(, 4.3.32767]", "System.Formats.Asn1": "(, 9.0.32767]", "System.Formats.Tar": "(, 9.0.32767]", "System.Globalization": "(, 4.3.32767]", "System.Globalization.Calendars": "(, 4.3.32767]", "System.Globalization.Extensions": "(, 4.3.32767]", "System.IO": "(, 4.3.32767]", "System.IO.Compression": "(, 4.3.32767]", "System.IO.Compression.ZipFile": "(, 4.3.32767]", "System.IO.FileSystem": "(, 4.3.32767]", "System.IO.FileSystem.AccessControl": "(, 5.0.32767]", "System.IO.FileSystem.DriveInfo": "(, 4.3.32767]", "System.IO.FileSystem.Primitives": "(, 4.3.32767]", "System.IO.FileSystem.Watcher": "(, 4.3.32767]", "System.IO.IsolatedStorage": "(, 4.3.32767]", "System.IO.MemoryMappedFiles": "(, 4.3.32767]", "System.IO.Pipelines": "(, 9.0.32767]", "System.IO.Pipes": "(, 4.3.32767]", "System.IO.Pipes.AccessControl": "(, 4.6.32767]", "System.IO.UnmanagedMemoryStream": "(, 4.3.32767]", "System.Linq": "(, 4.3.32767]", "System.Linq.Expressions": "(, 4.3.32767]", "System.Linq.Parallel": "(, 4.3.32767]", "System.Linq.Queryable": "(, 4.3.32767]", "System.Memory": "(, 5.0.32767]", "System.Net.Http": "(, 4.3.32767]", "System.Net.Http.Json": "(, 9.0.32767]", "System.Net.NameResolution": "(, 4.3.32767]", "System.Net.NetworkInformation": "(, 4.3.32767]", "System.Net.Ping": "(, 4.3.32767]", "System.Net.Primitives": "(, 4.3.32767]", "System.Net.Requests": "(, 4.3.32767]", "System.Net.Security": "(, 4.3.32767]", "System.Net.Sockets": "(, 4.3.32767]", "System.Net.WebHeaderCollection": "(, 4.3.32767]", "System.Net.WebSockets": "(, 4.3.32767]", "System.Net.WebSockets.Client": "(, 4.3.32767]", "System.Numerics.Vectors": "(, 5.0.32767]", "System.ObjectModel": "(, 4.3.32767]", "System.Private.DataContractSerialization": "(, 4.3.32767]", "System.Private.Uri": "(, 4.3.32767]", "System.Reflection": "(, 4.3.32767]", "System.Reflection.DispatchProxy": "(, 6.0.32767]", "System.Reflection.Emit": "(, 4.7.32767]", "System.Reflection.Emit.ILGeneration": "(, 4.7.32767]", "System.Reflection.Emit.Lightweight": "(, 4.7.32767]", "System.Reflection.Extensions": "(, 4.3.32767]", "System.Reflection.Metadata": "(, 9.0.32767]", "System.Reflection.Primitives": "(, 4.3.32767]", "System.Reflection.TypeExtensions": "(, 4.7.32767]", "System.Resources.Reader": "(, 4.3.32767]", "System.Resources.ResourceManager": "(, 4.3.32767]", "System.Resources.Writer": "(, 4.3.32767]", "System.Runtime": "(, 4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(, 7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(, 4.3.32767]", "System.Runtime.Extensions": "(, 4.3.32767]", "System.Runtime.Handles": "(, 4.3.32767]", "System.Runtime.InteropServices": "(, 4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(, 4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(, 4.3.32767]", "System.Runtime.Loader": "(, 4.3.32767]", "System.Runtime.Numerics": "(, 4.3.32767]", "System.Runtime.Serialization.Formatters": "(, 4.3.32767]", "System.Runtime.Serialization.Json": "(, 4.3.32767]", "System.Runtime.Serialization.Primitives": "(, 4.3.32767]", "System.Runtime.Serialization.Xml": "(, 4.3.32767]", "System.Runtime.WindowsRuntime": "(, 4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(, 4.7.32767]", "System.Security.AccessControl": "(, 6.0.32767]", "System.Security.Claims": "(, 4.3.32767]", "System.Security.Cryptography.Algorithms": "(, 4.3.32767]", "System.Security.Cryptography.Cng": "(, 4.6.32767]", "System.Security.Cryptography.Csp": "(, 4.3.32767]", "System.Security.Cryptography.Encoding": "(, 4.3.32767]", "System.Security.Cryptography.OpenSsl": "(, 5.0.32767]", "System.Security.Cryptography.Primitives": "(, 4.3.32767]", "System.Security.Cryptography.X509Certificates": "(, 4.3.32767]", "System.Security.Cryptography.Xml": "(, 4.4.32767]", "System.Security.Principal": "(, 4.3.32767]", "System.Security.Principal.Windows": "(, 5.0.32767]", "System.Security.SecureString": "(, 4.3.32767]", "System.Text.Encoding": "(, 4.3.32767]", "System.Text.Encoding.CodePages": "(, 9.0.32767]", "System.Text.Encoding.Extensions": "(, 4.3.32767]", "System.Text.Encodings.Web": "(, 9.0.32767]", "System.Text.Json": "(, 9.0.32767]", "System.Text.RegularExpressions": "(, 4.3.32767]", "System.Threading": "(, 4.3.32767]", "System.Threading.Channels": "(, 9.0.32767]", "System.Threading.Overlapped": "(, 4.3.32767]", "System.Threading.Tasks": "(, 4.3.32767]", "System.Threading.Tasks.Dataflow": "(, 9.0.32767]", "System.Threading.Tasks.Extensions": "(, 5.0.32767]", "System.Threading.Tasks.Parallel": "(, 4.3.32767]", "System.Threading.Thread": "(, 4.3.32767]", "System.Threading.ThreadPool": "(, 4.3.32767]", "System.Threading.Timer": "(, 4.3.32767]", "System.ValueTuple": "(, 4.5.32767]", "System.Xml.ReaderWriter": "(, 4.3.32767]", "System.Xml.XDocument": "(, 4.3.32767]", "System.Xml.XmlDocument": "(, 4.3.32767]", "System.Xml.XmlSerializer": "(, 4.3.32767]", "System.Xml.XPath": "(, 4.3.32767]", "System.Xml.XPath.XDocument": "(, 5.0.32767]"}}}}, "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Entities\\NotifyMaster.Entities.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Entities\\NotifyMaster.Entities.csproj", "projectName": "NotifyMaster.Entities", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Entities\\NotifyMaster.Entities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Entities\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[9.0.5, 9.0.5]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.5.25277.114/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(, 4.7.32767]", "Microsoft.NETCore.App": "(, 2.1.32767]", "Microsoft.VisualBasic": "(, 10.4.32767]", "Microsoft.Win32.Primitives": "(, 4.3.32767]", "Microsoft.Win32.Registry": "(, 5.0.32767]", "runtime.any.System.Collections": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.any.System.Globalization": "(, 4.3.32767]", "runtime.any.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.any.System.IO": "(, 4.3.32767]", "runtime.any.System.Reflection": "(, 4.3.32767]", "runtime.any.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.any.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.any.System.Runtime": "(, 4.3.32767]", "runtime.any.System.Runtime.Handles": "(, 4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.any.System.Text.Encoding": "(, 4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.any.System.Threading.Tasks": "(, 4.3.32767]", "runtime.any.System.Threading.Timer": "(, 4.3.32767]", "runtime.aot.System.Collections": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.aot.System.Globalization": "(, 4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.aot.System.IO": "(, 4.3.32767]", "runtime.aot.System.Reflection": "(, 4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.aot.System.Runtime": "(, 4.3.32767]", "runtime.aot.System.Runtime.Handles": "(, 4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.aot.System.Threading.Tasks": "(, 4.3.32767]", "runtime.aot.System.Threading.Timer": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.unix.System.Console": "(, 4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.unix.System.IO.FileSystem": "(, 4.3.32767]", "runtime.unix.System.Net.Primitives": "(, 4.3.32767]", "runtime.unix.System.Net.Sockets": "(, 4.3.32767]", "runtime.unix.System.Private.Uri": "(, 4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.win.System.Console": "(, 4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.win.System.IO.FileSystem": "(, 4.3.32767]", "runtime.win.System.Net.Primitives": "(, 4.3.32767]", "runtime.win.System.Net.Sockets": "(, 4.3.32767]", "runtime.win.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7.System.Private.Uri": "(, 4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(, 4.3.32767]", "System.AppContext": "(, 4.3.32767]", "System.Buffers": "(, 5.0.32767]", "System.Collections": "(, 4.3.32767]", "System.Collections.Concurrent": "(, 4.3.32767]", "System.Collections.Immutable": "(, 9.0.32767]", "System.Collections.NonGeneric": "(, 4.3.32767]", "System.Collections.Specialized": "(, 4.3.32767]", "System.ComponentModel": "(, 4.3.32767]", "System.ComponentModel.Annotations": "(, 5.0.32767]", "System.ComponentModel.EventBasedAsync": "(, 4.3.32767]", "System.ComponentModel.Primitives": "(, 4.3.32767]", "System.ComponentModel.TypeConverter": "(, 4.3.32767]", "System.Console": "(, 4.3.32767]", "System.Data.Common": "(, 4.3.32767]", "System.Data.DataSetExtensions": "(, 4.5.32767]", "System.Diagnostics.Contracts": "(, 4.3.32767]", "System.Diagnostics.Debug": "(, 4.3.32767]", "System.Diagnostics.DiagnosticSource": "(, 9.0.32767]", "System.Diagnostics.FileVersionInfo": "(, 4.3.32767]", "System.Diagnostics.Process": "(, 4.3.32767]", "System.Diagnostics.StackTrace": "(, 4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(, 4.3.32767]", "System.Diagnostics.Tools": "(, 4.3.32767]", "System.Diagnostics.TraceSource": "(, 4.3.32767]", "System.Diagnostics.Tracing": "(, 4.3.32767]", "System.Drawing.Primitives": "(, 4.3.32767]", "System.Dynamic.Runtime": "(, 4.3.32767]", "System.Formats.Asn1": "(, 9.0.32767]", "System.Formats.Tar": "(, 9.0.32767]", "System.Globalization": "(, 4.3.32767]", "System.Globalization.Calendars": "(, 4.3.32767]", "System.Globalization.Extensions": "(, 4.3.32767]", "System.IO": "(, 4.3.32767]", "System.IO.Compression": "(, 4.3.32767]", "System.IO.Compression.ZipFile": "(, 4.3.32767]", "System.IO.FileSystem": "(, 4.3.32767]", "System.IO.FileSystem.AccessControl": "(, 5.0.32767]", "System.IO.FileSystem.DriveInfo": "(, 4.3.32767]", "System.IO.FileSystem.Primitives": "(, 4.3.32767]", "System.IO.FileSystem.Watcher": "(, 4.3.32767]", "System.IO.IsolatedStorage": "(, 4.3.32767]", "System.IO.MemoryMappedFiles": "(, 4.3.32767]", "System.IO.Pipelines": "(, 9.0.32767]", "System.IO.Pipes": "(, 4.3.32767]", "System.IO.Pipes.AccessControl": "(, 4.6.32767]", "System.IO.UnmanagedMemoryStream": "(, 4.3.32767]", "System.Linq": "(, 4.3.32767]", "System.Linq.Expressions": "(, 4.3.32767]", "System.Linq.Parallel": "(, 4.3.32767]", "System.Linq.Queryable": "(, 4.3.32767]", "System.Memory": "(, 5.0.32767]", "System.Net.Http": "(, 4.3.32767]", "System.Net.Http.Json": "(, 9.0.32767]", "System.Net.NameResolution": "(, 4.3.32767]", "System.Net.NetworkInformation": "(, 4.3.32767]", "System.Net.Ping": "(, 4.3.32767]", "System.Net.Primitives": "(, 4.3.32767]", "System.Net.Requests": "(, 4.3.32767]", "System.Net.Security": "(, 4.3.32767]", "System.Net.Sockets": "(, 4.3.32767]", "System.Net.WebHeaderCollection": "(, 4.3.32767]", "System.Net.WebSockets": "(, 4.3.32767]", "System.Net.WebSockets.Client": "(, 4.3.32767]", "System.Numerics.Vectors": "(, 5.0.32767]", "System.ObjectModel": "(, 4.3.32767]", "System.Private.DataContractSerialization": "(, 4.3.32767]", "System.Private.Uri": "(, 4.3.32767]", "System.Reflection": "(, 4.3.32767]", "System.Reflection.DispatchProxy": "(, 6.0.32767]", "System.Reflection.Emit": "(, 4.7.32767]", "System.Reflection.Emit.ILGeneration": "(, 4.7.32767]", "System.Reflection.Emit.Lightweight": "(, 4.7.32767]", "System.Reflection.Extensions": "(, 4.3.32767]", "System.Reflection.Metadata": "(, 9.0.32767]", "System.Reflection.Primitives": "(, 4.3.32767]", "System.Reflection.TypeExtensions": "(, 4.7.32767]", "System.Resources.Reader": "(, 4.3.32767]", "System.Resources.ResourceManager": "(, 4.3.32767]", "System.Resources.Writer": "(, 4.3.32767]", "System.Runtime": "(, 4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(, 7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(, 4.3.32767]", "System.Runtime.Extensions": "(, 4.3.32767]", "System.Runtime.Handles": "(, 4.3.32767]", "System.Runtime.InteropServices": "(, 4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(, 4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(, 4.3.32767]", "System.Runtime.Loader": "(, 4.3.32767]", "System.Runtime.Numerics": "(, 4.3.32767]", "System.Runtime.Serialization.Formatters": "(, 4.3.32767]", "System.Runtime.Serialization.Json": "(, 4.3.32767]", "System.Runtime.Serialization.Primitives": "(, 4.3.32767]", "System.Runtime.Serialization.Xml": "(, 4.3.32767]", "System.Runtime.WindowsRuntime": "(, 4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(, 4.7.32767]", "System.Security.AccessControl": "(, 6.0.32767]", "System.Security.Claims": "(, 4.3.32767]", "System.Security.Cryptography.Algorithms": "(, 4.3.32767]", "System.Security.Cryptography.Cng": "(, 4.6.32767]", "System.Security.Cryptography.Csp": "(, 4.3.32767]", "System.Security.Cryptography.Encoding": "(, 4.3.32767]", "System.Security.Cryptography.OpenSsl": "(, 5.0.32767]", "System.Security.Cryptography.Primitives": "(, 4.3.32767]", "System.Security.Cryptography.X509Certificates": "(, 4.3.32767]", "System.Security.Cryptography.Xml": "(, 4.4.32767]", "System.Security.Principal": "(, 4.3.32767]", "System.Security.Principal.Windows": "(, 5.0.32767]", "System.Security.SecureString": "(, 4.3.32767]", "System.Text.Encoding": "(, 4.3.32767]", "System.Text.Encoding.CodePages": "(, 9.0.32767]", "System.Text.Encoding.Extensions": "(, 4.3.32767]", "System.Text.Encodings.Web": "(, 9.0.32767]", "System.Text.Json": "(, 9.0.32767]", "System.Text.RegularExpressions": "(, 4.3.32767]", "System.Threading": "(, 4.3.32767]", "System.Threading.Channels": "(, 9.0.32767]", "System.Threading.Overlapped": "(, 4.3.32767]", "System.Threading.Tasks": "(, 4.3.32767]", "System.Threading.Tasks.Dataflow": "(, 9.0.32767]", "System.Threading.Tasks.Extensions": "(, 5.0.32767]", "System.Threading.Tasks.Parallel": "(, 4.3.32767]", "System.Threading.Thread": "(, 4.3.32767]", "System.Threading.ThreadPool": "(, 4.3.32767]", "System.Threading.Timer": "(, 4.3.32767]", "System.ValueTuple": "(, 4.5.32767]", "System.Xml.ReaderWriter": "(, 4.3.32767]", "System.Xml.XDocument": "(, 4.3.32767]", "System.Xml.XmlDocument": "(, 4.3.32767]", "System.Xml.XmlSerializer": "(, 4.3.32767]", "System.Xml.XPath": "(, 4.3.32767]", "System.Xml.XPath.XDocument": "(, 5.0.32767]"}}}}, "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj", "projectName": "PluginCore", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[8.12.1, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.12.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[9.0.5, 9.0.5]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.5.25277.114/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(, 4.7.32767]", "Microsoft.NETCore.App": "(, 2.1.32767]", "Microsoft.VisualBasic": "(, 10.4.32767]", "Microsoft.Win32.Primitives": "(, 4.3.32767]", "Microsoft.Win32.Registry": "(, 5.0.32767]", "runtime.any.System.Collections": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.any.System.Globalization": "(, 4.3.32767]", "runtime.any.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.any.System.IO": "(, 4.3.32767]", "runtime.any.System.Reflection": "(, 4.3.32767]", "runtime.any.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.any.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.any.System.Runtime": "(, 4.3.32767]", "runtime.any.System.Runtime.Handles": "(, 4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.any.System.Text.Encoding": "(, 4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.any.System.Threading.Tasks": "(, 4.3.32767]", "runtime.any.System.Threading.Timer": "(, 4.3.32767]", "runtime.aot.System.Collections": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.aot.System.Globalization": "(, 4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.aot.System.IO": "(, 4.3.32767]", "runtime.aot.System.Reflection": "(, 4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.aot.System.Runtime": "(, 4.3.32767]", "runtime.aot.System.Runtime.Handles": "(, 4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.aot.System.Threading.Tasks": "(, 4.3.32767]", "runtime.aot.System.Threading.Timer": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.unix.System.Console": "(, 4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.unix.System.IO.FileSystem": "(, 4.3.32767]", "runtime.unix.System.Net.Primitives": "(, 4.3.32767]", "runtime.unix.System.Net.Sockets": "(, 4.3.32767]", "runtime.unix.System.Private.Uri": "(, 4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.win.System.Console": "(, 4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.win.System.IO.FileSystem": "(, 4.3.32767]", "runtime.win.System.Net.Primitives": "(, 4.3.32767]", "runtime.win.System.Net.Sockets": "(, 4.3.32767]", "runtime.win.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7.System.Private.Uri": "(, 4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(, 4.3.32767]", "System.AppContext": "(, 4.3.32767]", "System.Buffers": "(, 5.0.32767]", "System.Collections": "(, 4.3.32767]", "System.Collections.Concurrent": "(, 4.3.32767]", "System.Collections.Immutable": "(, 9.0.32767]", "System.Collections.NonGeneric": "(, 4.3.32767]", "System.Collections.Specialized": "(, 4.3.32767]", "System.ComponentModel": "(, 4.3.32767]", "System.ComponentModel.Annotations": "(, 5.0.32767]", "System.ComponentModel.EventBasedAsync": "(, 4.3.32767]", "System.ComponentModel.Primitives": "(, 4.3.32767]", "System.ComponentModel.TypeConverter": "(, 4.3.32767]", "System.Console": "(, 4.3.32767]", "System.Data.Common": "(, 4.3.32767]", "System.Data.DataSetExtensions": "(, 4.5.32767]", "System.Diagnostics.Contracts": "(, 4.3.32767]", "System.Diagnostics.Debug": "(, 4.3.32767]", "System.Diagnostics.DiagnosticSource": "(, 9.0.32767]", "System.Diagnostics.FileVersionInfo": "(, 4.3.32767]", "System.Diagnostics.Process": "(, 4.3.32767]", "System.Diagnostics.StackTrace": "(, 4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(, 4.3.32767]", "System.Diagnostics.Tools": "(, 4.3.32767]", "System.Diagnostics.TraceSource": "(, 4.3.32767]", "System.Diagnostics.Tracing": "(, 4.3.32767]", "System.Drawing.Primitives": "(, 4.3.32767]", "System.Dynamic.Runtime": "(, 4.3.32767]", "System.Formats.Asn1": "(, 9.0.32767]", "System.Formats.Tar": "(, 9.0.32767]", "System.Globalization": "(, 4.3.32767]", "System.Globalization.Calendars": "(, 4.3.32767]", "System.Globalization.Extensions": "(, 4.3.32767]", "System.IO": "(, 4.3.32767]", "System.IO.Compression": "(, 4.3.32767]", "System.IO.Compression.ZipFile": "(, 4.3.32767]", "System.IO.FileSystem": "(, 4.3.32767]", "System.IO.FileSystem.AccessControl": "(, 5.0.32767]", "System.IO.FileSystem.DriveInfo": "(, 4.3.32767]", "System.IO.FileSystem.Primitives": "(, 4.3.32767]", "System.IO.FileSystem.Watcher": "(, 4.3.32767]", "System.IO.IsolatedStorage": "(, 4.3.32767]", "System.IO.MemoryMappedFiles": "(, 4.3.32767]", "System.IO.Pipelines": "(, 9.0.32767]", "System.IO.Pipes": "(, 4.3.32767]", "System.IO.Pipes.AccessControl": "(, 4.6.32767]", "System.IO.UnmanagedMemoryStream": "(, 4.3.32767]", "System.Linq": "(, 4.3.32767]", "System.Linq.Expressions": "(, 4.3.32767]", "System.Linq.Parallel": "(, 4.3.32767]", "System.Linq.Queryable": "(, 4.3.32767]", "System.Memory": "(, 5.0.32767]", "System.Net.Http": "(, 4.3.32767]", "System.Net.Http.Json": "(, 9.0.32767]", "System.Net.NameResolution": "(, 4.3.32767]", "System.Net.NetworkInformation": "(, 4.3.32767]", "System.Net.Ping": "(, 4.3.32767]", "System.Net.Primitives": "(, 4.3.32767]", "System.Net.Requests": "(, 4.3.32767]", "System.Net.Security": "(, 4.3.32767]", "System.Net.Sockets": "(, 4.3.32767]", "System.Net.WebHeaderCollection": "(, 4.3.32767]", "System.Net.WebSockets": "(, 4.3.32767]", "System.Net.WebSockets.Client": "(, 4.3.32767]", "System.Numerics.Vectors": "(, 5.0.32767]", "System.ObjectModel": "(, 4.3.32767]", "System.Private.DataContractSerialization": "(, 4.3.32767]", "System.Private.Uri": "(, 4.3.32767]", "System.Reflection": "(, 4.3.32767]", "System.Reflection.DispatchProxy": "(, 6.0.32767]", "System.Reflection.Emit": "(, 4.7.32767]", "System.Reflection.Emit.ILGeneration": "(, 4.7.32767]", "System.Reflection.Emit.Lightweight": "(, 4.7.32767]", "System.Reflection.Extensions": "(, 4.3.32767]", "System.Reflection.Metadata": "(, 9.0.32767]", "System.Reflection.Primitives": "(, 4.3.32767]", "System.Reflection.TypeExtensions": "(, 4.7.32767]", "System.Resources.Reader": "(, 4.3.32767]", "System.Resources.ResourceManager": "(, 4.3.32767]", "System.Resources.Writer": "(, 4.3.32767]", "System.Runtime": "(, 4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(, 7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(, 4.3.32767]", "System.Runtime.Extensions": "(, 4.3.32767]", "System.Runtime.Handles": "(, 4.3.32767]", "System.Runtime.InteropServices": "(, 4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(, 4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(, 4.3.32767]", "System.Runtime.Loader": "(, 4.3.32767]", "System.Runtime.Numerics": "(, 4.3.32767]", "System.Runtime.Serialization.Formatters": "(, 4.3.32767]", "System.Runtime.Serialization.Json": "(, 4.3.32767]", "System.Runtime.Serialization.Primitives": "(, 4.3.32767]", "System.Runtime.Serialization.Xml": "(, 4.3.32767]", "System.Runtime.WindowsRuntime": "(, 4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(, 4.7.32767]", "System.Security.AccessControl": "(, 6.0.32767]", "System.Security.Claims": "(, 4.3.32767]", "System.Security.Cryptography.Algorithms": "(, 4.3.32767]", "System.Security.Cryptography.Cng": "(, 4.6.32767]", "System.Security.Cryptography.Csp": "(, 4.3.32767]", "System.Security.Cryptography.Encoding": "(, 4.3.32767]", "System.Security.Cryptography.OpenSsl": "(, 5.0.32767]", "System.Security.Cryptography.Primitives": "(, 4.3.32767]", "System.Security.Cryptography.X509Certificates": "(, 4.3.32767]", "System.Security.Cryptography.Xml": "(, 4.4.32767]", "System.Security.Principal": "(, 4.3.32767]", "System.Security.Principal.Windows": "(, 5.0.32767]", "System.Security.SecureString": "(, 4.3.32767]", "System.Text.Encoding": "(, 4.3.32767]", "System.Text.Encoding.CodePages": "(, 9.0.32767]", "System.Text.Encoding.Extensions": "(, 4.3.32767]", "System.Text.Encodings.Web": "(, 9.0.32767]", "System.Text.Json": "(, 9.0.32767]", "System.Text.RegularExpressions": "(, 4.3.32767]", "System.Threading": "(, 4.3.32767]", "System.Threading.Channels": "(, 9.0.32767]", "System.Threading.Overlapped": "(, 4.3.32767]", "System.Threading.Tasks": "(, 4.3.32767]", "System.Threading.Tasks.Dataflow": "(, 9.0.32767]", "System.Threading.Tasks.Extensions": "(, 5.0.32767]", "System.Threading.Tasks.Parallel": "(, 4.3.32767]", "System.Threading.Thread": "(, 4.3.32767]", "System.Threading.ThreadPool": "(, 4.3.32767]", "System.Threading.Timer": "(, 4.3.32767]", "System.ValueTuple": "(, 4.5.32767]", "System.Xml.ReaderWriter": "(, 4.3.32767]", "System.Xml.XDocument": "(, 4.3.32767]", "System.Xml.XmlDocument": "(, 4.3.32767]", "System.Xml.XmlSerializer": "(, 4.3.32767]", "System.Xml.XPath": "(, 4.3.32767]", "System.Xml.XPath.XDocument": "(, 5.0.32767]"}}}}, "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Implementations\\NotifyMasterApi\\NotifyMasterApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Implementations\\NotifyMasterApi\\NotifyMasterApi.csproj", "projectName": "NotifyMasterApi", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Implementations\\NotifyMasterApi\\NotifyMasterApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Implementations\\NotifyMasterApi\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj"}, "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj"}, "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AWSSDK.S3": {"target": "Package", "version": "[4.0.3, )"}, "Azure.Storage.Blobs": {"target": "Package", "version": "[12.24.1, )"}, "FastEndpoints": {"target": "Package", "version": "[6.2.0, )"}, "Hangfire.AspNetCore": {"target": "Package", "version": "[1.8.20, )"}, "Hangfire.Core": {"target": "Package", "version": "[1.8.20, )"}, "Hangfire.InMemory": {"target": "Package", "version": "[1.0.0, )"}, "Hangfire.PostgreSql": {"target": "Package", "version": "[1.20.12, )"}, "Hangfire.SqlServer": {"target": "Package", "version": "[1.8.20, )"}, "MediatR": {"target": "Package", "version": "[12.5.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.AspNetCore.SignalR": {"target": "Package", "version": "[1.2.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[9.0.6, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[9.0.0-preview.1, )"}, "Scalar.AspNetCore": {"target": "Package", "version": "[2.5.3, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.Debug": {"target": "Package", "version": "[3.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.6, )"}, "Terminal.Gui": {"target": "Package", "version": "[1.15.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[9.0.5, 9.0.5]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.5.25277.114/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.AspNetCore": "(, 9.0.32767]", "Microsoft.AspNetCore.Antiforgery": "(, 9.0.32767]", "Microsoft.AspNetCore.Authentication": "(, 9.0.32767]", "Microsoft.AspNetCore.Authentication.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.Authentication.BearerToken": "(, 9.0.32767]", "Microsoft.AspNetCore.Authentication.Cookies": "(, 9.0.32767]", "Microsoft.AspNetCore.Authentication.Core": "(, 9.0.32767]", "Microsoft.AspNetCore.Authentication.OAuth": "(, 9.0.32767]", "Microsoft.AspNetCore.Authorization": "(, 9.0.32767]", "Microsoft.AspNetCore.Authorization.Policy": "(, 9.0.32767]", "Microsoft.AspNetCore.Components": "(, 9.0.32767]", "Microsoft.AspNetCore.Components.Authorization": "(, 9.0.32767]", "Microsoft.AspNetCore.Components.Endpoints": "(, 9.0.32767]", "Microsoft.AspNetCore.Components.Forms": "(, 9.0.32767]", "Microsoft.AspNetCore.Components.Server": "(, 9.0.32767]", "Microsoft.AspNetCore.Components.Web": "(, 9.0.32767]", "Microsoft.AspNetCore.Connections.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.CookiePolicy": "(, 9.0.32767]", "Microsoft.AspNetCore.Cors": "(, 9.0.32767]", "Microsoft.AspNetCore.Cryptography.Internal": "(, 9.0.32767]", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "(, 9.0.32767]", "Microsoft.AspNetCore.DataProtection": "(, 9.0.32767]", "Microsoft.AspNetCore.DataProtection.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.DataProtection.Extensions": "(, 9.0.32767]", "Microsoft.AspNetCore.Diagnostics": "(, 9.0.32767]", "Microsoft.AspNetCore.Diagnostics.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "(, 9.0.32767]", "Microsoft.AspNetCore.HostFiltering": "(, 9.0.32767]", "Microsoft.AspNetCore.Hosting": "(, 9.0.32767]", "Microsoft.AspNetCore.Hosting.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.Html.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.Http": "(, 9.0.32767]", "Microsoft.AspNetCore.Http.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.Http.Connections": "(, 9.0.32767]", "Microsoft.AspNetCore.Http.Connections.Common": "(, 9.0.32767]", "Microsoft.AspNetCore.Http.Extensions": "(, 9.0.32767]", "Microsoft.AspNetCore.Http.Features": "(, 9.0.32767]", "Microsoft.AspNetCore.Http.Results": "(, 9.0.32767]", "Microsoft.AspNetCore.HttpLogging": "(, 9.0.32767]", "Microsoft.AspNetCore.HttpOverrides": "(, 9.0.32767]", "Microsoft.AspNetCore.HttpsPolicy": "(, 9.0.32767]", "Microsoft.AspNetCore.Identity": "(, 9.0.32767]", "Microsoft.AspNetCore.Localization": "(, 9.0.32767]", "Microsoft.AspNetCore.Localization.Routing": "(, 9.0.32767]", "Microsoft.AspNetCore.Metadata": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.ApiExplorer": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.Core": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.Cors": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.DataAnnotations": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.Formatters.Json": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.Localization": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.Razor": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.RazorPages": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.TagHelpers": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.ViewFeatures": "(, 9.0.32767]", "Microsoft.AspNetCore.OutputCaching": "(, 9.0.32767]", "Microsoft.AspNetCore.RateLimiting": "(, 9.0.32767]", "Microsoft.AspNetCore.Razor": "(, 9.0.32767]", "Microsoft.AspNetCore.Razor.Runtime": "(, 9.0.32767]", "Microsoft.AspNetCore.RequestDecompression": "(, 9.0.32767]", "Microsoft.AspNetCore.ResponseCaching": "(, 9.0.32767]", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.ResponseCompression": "(, 9.0.32767]", "Microsoft.AspNetCore.Rewrite": "(, 9.0.32767]", "Microsoft.AspNetCore.Routing": "(, 9.0.32767]", "Microsoft.AspNetCore.Routing.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.Server.HttpSys": "(, 9.0.32767]", "Microsoft.AspNetCore.Server.IIS": "(, 9.0.32767]", "Microsoft.AspNetCore.Server.IISIntegration": "(, 9.0.32767]", "Microsoft.AspNetCore.Server.Kestrel": "(, 9.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Core": "(, 9.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes": "(, 9.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic": "(, 9.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "(, 9.0.32767]", "Microsoft.AspNetCore.Session": "(, 9.0.32767]", "Microsoft.AspNetCore.SignalR": "(, 9.0.32767]", "Microsoft.AspNetCore.SignalR.Common": "(, 9.0.32767]", "Microsoft.AspNetCore.SignalR.Core": "(, 9.0.32767]", "Microsoft.AspNetCore.SignalR.Protocols.Json": "(, 9.0.32767]", "Microsoft.AspNetCore.StaticAssets": "(, 9.0.32767]", "Microsoft.AspNetCore.StaticFiles": "(, 9.0.32767]", "Microsoft.AspNetCore.WebSockets": "(, 9.0.32767]", "Microsoft.AspNetCore.WebUtilities": "(, 9.0.32767]", "Microsoft.CSharp": "(, 4.7.32767]", "Microsoft.Extensions.Caching.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.Caching.Memory": "(, 9.0.32767]", "Microsoft.Extensions.Configuration": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.Binder": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.CommandLine": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.EnvironmentVariables": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.FileExtensions": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.Ini": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.Json": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.KeyPerFile": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.UserSecrets": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.Xml": "(, 9.0.32767]", "Microsoft.Extensions.DependencyInjection": "(, 9.0.32767]", "Microsoft.Extensions.DependencyInjection.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.Diagnostics": "(, 9.0.32767]", "Microsoft.Extensions.Diagnostics.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.Diagnostics.HealthChecks": "(, 9.0.32767]", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.Features": "(, 9.0.32767]", "Microsoft.Extensions.FileProviders.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.FileProviders.Composite": "(, 9.0.32767]", "Microsoft.Extensions.FileProviders.Embedded": "(, 9.0.32767]", "Microsoft.Extensions.FileProviders.Physical": "(, 9.0.32767]", "Microsoft.Extensions.FileSystemGlobbing": "(, 9.0.32767]", "Microsoft.Extensions.Hosting": "(, 9.0.32767]", "Microsoft.Extensions.Hosting.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.Http": "(, 9.0.32767]", "Microsoft.Extensions.Identity.Core": "(, 9.0.32767]", "Microsoft.Extensions.Identity.Stores": "(, 9.0.32767]", "Microsoft.Extensions.Localization": "(, 9.0.32767]", "Microsoft.Extensions.Localization.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.Logging": "(, 9.0.32767]", "Microsoft.Extensions.Logging.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.Logging.Configuration": "(, 9.0.32767]", "Microsoft.Extensions.Logging.Console": "(, 9.0.32767]", "Microsoft.Extensions.Logging.Debug": "(, 9.0.32767]", "Microsoft.Extensions.Logging.EventLog": "(, 9.0.32767]", "Microsoft.Extensions.Logging.EventSource": "(, 9.0.32767]", "Microsoft.Extensions.Logging.TraceSource": "(, 9.0.32767]", "Microsoft.Extensions.ObjectPool": "(, 9.0.32767]", "Microsoft.Extensions.Options": "(, 9.0.32767]", "Microsoft.Extensions.Options.ConfigurationExtensions": "(, 9.0.32767]", "Microsoft.Extensions.Options.DataAnnotations": "(, 9.0.32767]", "Microsoft.Extensions.Primitives": "(, 9.0.32767]", "Microsoft.Extensions.WebEncoders": "(, 9.0.32767]", "Microsoft.JSInterop": "(, 9.0.32767]", "Microsoft.Net.Http.Headers": "(, 9.0.32767]", "Microsoft.NETCore.App": "(, 2.1.32767]", "Microsoft.VisualBasic": "(, 10.4.32767]", "Microsoft.Win32.Primitives": "(, 4.3.32767]", "Microsoft.Win32.Registry": "(, 5.0.32767]", "Microsoft.Win32.SystemEvents": "(, 5.0.32767]", "runtime.any.System.Collections": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.any.System.Globalization": "(, 4.3.32767]", "runtime.any.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.any.System.IO": "(, 4.3.32767]", "runtime.any.System.Reflection": "(, 4.3.32767]", "runtime.any.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.any.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.any.System.Runtime": "(, 4.3.32767]", "runtime.any.System.Runtime.Handles": "(, 4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.any.System.Text.Encoding": "(, 4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.any.System.Threading.Tasks": "(, 4.3.32767]", "runtime.any.System.Threading.Timer": "(, 4.3.32767]", "runtime.aot.System.Collections": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.aot.System.Globalization": "(, 4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.aot.System.IO": "(, 4.3.32767]", "runtime.aot.System.Reflection": "(, 4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.aot.System.Runtime": "(, 4.3.32767]", "runtime.aot.System.Runtime.Handles": "(, 4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.aot.System.Threading.Tasks": "(, 4.3.32767]", "runtime.aot.System.Threading.Timer": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.unix.System.Console": "(, 4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.unix.System.IO.FileSystem": "(, 4.3.32767]", "runtime.unix.System.Net.Primitives": "(, 4.3.32767]", "runtime.unix.System.Net.Sockets": "(, 4.3.32767]", "runtime.unix.System.Private.Uri": "(, 4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.win.System.Console": "(, 4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.win.System.IO.FileSystem": "(, 4.3.32767]", "runtime.win.System.Net.Primitives": "(, 4.3.32767]", "runtime.win.System.Net.Sockets": "(, 4.3.32767]", "runtime.win.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7.System.Private.Uri": "(, 4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(, 4.3.32767]", "System.AppContext": "(, 4.3.32767]", "System.Buffers": "(, 5.0.32767]", "System.Collections": "(, 4.3.32767]", "System.Collections.Concurrent": "(, 4.3.32767]", "System.Collections.Immutable": "(, 9.0.32767]", "System.Collections.NonGeneric": "(, 4.3.32767]", "System.Collections.Specialized": "(, 4.3.32767]", "System.ComponentModel": "(, 4.3.32767]", "System.ComponentModel.Annotations": "(, 5.0.32767]", "System.ComponentModel.EventBasedAsync": "(, 4.3.32767]", "System.ComponentModel.Primitives": "(, 4.3.32767]", "System.ComponentModel.TypeConverter": "(, 4.3.32767]", "System.Console": "(, 4.3.32767]", "System.Data.Common": "(, 4.3.32767]", "System.Data.DataSetExtensions": "(, 4.5.32767]", "System.Diagnostics.Contracts": "(, 4.3.32767]", "System.Diagnostics.Debug": "(, 4.3.32767]", "System.Diagnostics.DiagnosticSource": "(, 9.0.32767]", "System.Diagnostics.EventLog": "(, 9.0.32767]", "System.Diagnostics.FileVersionInfo": "(, 4.3.32767]", "System.Diagnostics.Process": "(, 4.3.32767]", "System.Diagnostics.StackTrace": "(, 4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(, 4.3.32767]", "System.Diagnostics.Tools": "(, 4.3.32767]", "System.Diagnostics.TraceSource": "(, 4.3.32767]", "System.Diagnostics.Tracing": "(, 4.3.32767]", "System.Drawing.Common": "(, 5.0.32767]", "System.Drawing.Primitives": "(, 4.3.32767]", "System.Dynamic.Runtime": "(, 4.3.32767]", "System.Formats.Asn1": "(, 9.0.32767]", "System.Formats.Tar": "(, 9.0.32767]", "System.Globalization": "(, 4.3.32767]", "System.Globalization.Calendars": "(, 4.3.32767]", "System.Globalization.Extensions": "(, 4.3.32767]", "System.IO": "(, 4.3.32767]", "System.IO.Compression": "(, 4.3.32767]", "System.IO.Compression.ZipFile": "(, 4.3.32767]", "System.IO.FileSystem": "(, 4.3.32767]", "System.IO.FileSystem.AccessControl": "(, 5.0.32767]", "System.IO.FileSystem.DriveInfo": "(, 4.3.32767]", "System.IO.FileSystem.Primitives": "(, 4.3.32767]", "System.IO.FileSystem.Watcher": "(, 4.3.32767]", "System.IO.IsolatedStorage": "(, 4.3.32767]", "System.IO.MemoryMappedFiles": "(, 4.3.32767]", "System.IO.Pipelines": "(, 9.0.32767]", "System.IO.Pipes": "(, 4.3.32767]", "System.IO.Pipes.AccessControl": "(, 4.6.32767]", "System.IO.UnmanagedMemoryStream": "(, 4.3.32767]", "System.Linq": "(, 4.3.32767]", "System.Linq.Expressions": "(, 4.3.32767]", "System.Linq.Parallel": "(, 4.3.32767]", "System.Linq.Queryable": "(, 4.3.32767]", "System.Memory": "(, 5.0.32767]", "System.Net.Http": "(, 4.3.32767]", "System.Net.Http.Json": "(, 9.0.32767]", "System.Net.NameResolution": "(, 4.3.32767]", "System.Net.NetworkInformation": "(, 4.3.32767]", "System.Net.Ping": "(, 4.3.32767]", "System.Net.Primitives": "(, 4.3.32767]", "System.Net.Requests": "(, 4.3.32767]", "System.Net.Security": "(, 4.3.32767]", "System.Net.Sockets": "(, 4.3.32767]", "System.Net.WebHeaderCollection": "(, 4.3.32767]", "System.Net.WebSockets": "(, 4.3.32767]", "System.Net.WebSockets.Client": "(, 4.3.32767]", "System.Numerics.Vectors": "(, 5.0.32767]", "System.ObjectModel": "(, 4.3.32767]", "System.Private.DataContractSerialization": "(, 4.3.32767]", "System.Private.Uri": "(, 4.3.32767]", "System.Reflection": "(, 4.3.32767]", "System.Reflection.DispatchProxy": "(, 6.0.32767]", "System.Reflection.Emit": "(, 4.7.32767]", "System.Reflection.Emit.ILGeneration": "(, 4.7.32767]", "System.Reflection.Emit.Lightweight": "(, 4.7.32767]", "System.Reflection.Extensions": "(, 4.3.32767]", "System.Reflection.Metadata": "(, 9.0.32767]", "System.Reflection.Primitives": "(, 4.3.32767]", "System.Reflection.TypeExtensions": "(, 4.7.32767]", "System.Resources.Reader": "(, 4.3.32767]", "System.Resources.ResourceManager": "(, 4.3.32767]", "System.Resources.Writer": "(, 4.3.32767]", "System.Runtime": "(, 4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(, 7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(, 4.3.32767]", "System.Runtime.Extensions": "(, 4.3.32767]", "System.Runtime.Handles": "(, 4.3.32767]", "System.Runtime.InteropServices": "(, 4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(, 4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(, 4.3.32767]", "System.Runtime.Loader": "(, 4.3.32767]", "System.Runtime.Numerics": "(, 4.3.32767]", "System.Runtime.Serialization.Formatters": "(, 4.3.32767]", "System.Runtime.Serialization.Json": "(, 4.3.32767]", "System.Runtime.Serialization.Primitives": "(, 4.3.32767]", "System.Runtime.Serialization.Xml": "(, 4.3.32767]", "System.Runtime.WindowsRuntime": "(, 4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(, 4.7.32767]", "System.Security.AccessControl": "(, 6.0.32767]", "System.Security.Claims": "(, 4.3.32767]", "System.Security.Cryptography.Algorithms": "(, 4.3.32767]", "System.Security.Cryptography.Cng": "(, 5.0.32767]", "System.Security.Cryptography.Csp": "(, 4.3.32767]", "System.Security.Cryptography.Encoding": "(, 4.3.32767]", "System.Security.Cryptography.OpenSsl": "(, 5.0.32767]", "System.Security.Cryptography.Pkcs": "(, 8.0.32767]", "System.Security.Cryptography.Primitives": "(, 4.3.32767]", "System.Security.Cryptography.X509Certificates": "(, 4.3.32767]", "System.Security.Cryptography.Xml": "(, 9.0.32767]", "System.Security.Permissions": "(, 5.0.32767]", "System.Security.Principal": "(, 4.3.32767]", "System.Security.Principal.Windows": "(, 5.0.32767]", "System.Security.SecureString": "(, 4.3.32767]", "System.Text.Encoding": "(, 4.3.32767]", "System.Text.Encoding.CodePages": "(, 9.0.32767]", "System.Text.Encoding.Extensions": "(, 4.3.32767]", "System.Text.Encodings.Web": "(, 9.0.32767]", "System.Text.Json": "(, 9.0.32767]", "System.Text.RegularExpressions": "(, 4.3.32767]", "System.Threading": "(, 4.3.32767]", "System.Threading.Channels": "(, 9.0.32767]", "System.Threading.Overlapped": "(, 4.3.32767]", "System.Threading.RateLimiting": "(, 9.0.32767]", "System.Threading.Tasks": "(, 4.3.32767]", "System.Threading.Tasks.Dataflow": "(, 9.0.32767]", "System.Threading.Tasks.Extensions": "(, 5.0.32767]", "System.Threading.Tasks.Parallel": "(, 4.3.32767]", "System.Threading.Thread": "(, 4.3.32767]", "System.Threading.ThreadPool": "(, 4.3.32767]", "System.Threading.Timer": "(, 4.3.32767]", "System.ValueTuple": "(, 4.5.32767]", "System.Windows.Extensions": "(, 5.0.32767]", "System.Xml.ReaderWriter": "(, 4.3.32767]", "System.Xml.XDocument": "(, 4.3.32767]", "System.Xml.XmlDocument": "(, 4.3.32767]", "System.Xml.XmlSerializer": "(, 4.3.32767]", "System.Xml.XPath": "(, 4.3.32767]", "System.Xml.XPath.XDocument": "(, 5.0.32767]"}}}}}}